// lib/mqtt/mqtt_websocket.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

import '../constant.dart';
import '../models/device.dart';
import '../models/user.dart';

class MqttHandler {
  // Constants
  static const String _defaultMqttDomain = 'elec.mn';
  static const String _mqttPath = '/mqtt';
  static const int _mqttPort = 8083;
  static const int _keepAlivePeriod = 60;
  static const int _checkDeviceTimeout = 5;
  static const int _maxReconnectAttempts = 10;

  // Get the MQTT broker URL
  String get mqttBrokerUrl => 'ws://${_getMqttDomain()}$_mqttPath';

  // Get the domain from constants or environment
  String _getMqttDomain() {
    return MQTT_Websocket_HOST.isNotEmpty
        ? MQTT_Websocket_HOST
        : _defaultMqttDomain;
  }

  final ValueNotifier<DeviceStatus> data =
      ValueNotifier<DeviceStatus>(DeviceStatus.initial());

  late final MqttServerClient _client;
  final String phoneNumber;
  final String? userDevice;

  int _reconnectAttempts = 0;
  Timer? _reconnectTimer;
  Timer? _deviceOnlineTimer;

  final VoidCallback? onConnectedCallback;
  final VoidCallback? onDisconnectedCallback;

  bool _isConnected = false;
  bool get isConnected => _isConnected;

  bool _isConnecting = false;
  bool get isConnecting => _isConnecting;

  late final String _clientId;

  Completer<bool>? _deviceOnlineCompleter;
  StreamSubscription<List<MqttReceivedMessage<MqttMessage?>>>?
      _updatesSubscription;

  bool _isDisposed = false;

  MqttHandler._internal({
    required this.phoneNumber,
    required this.userDevice,
    this.onConnectedCallback,
    this.onDisconnectedCallback,
  });

  static Future<MqttHandler> create({
    required String phoneNumber,
    required String? userDevice,
    VoidCallback? onConnectedCallback,
    VoidCallback? onDisconnectedCallback,
  }) async {
    final handler = MqttHandler._internal(
      phoneNumber: phoneNumber,
      userDevice: userDevice,
      onConnectedCallback: onConnectedCallback,
      onDisconnectedCallback: onDisconnectedCallback,
    );

    handler._initializeClientId();
    handler._initializeMqttClient();
    handler._listenToConnectivityChanges();

    return handler;
  }

  void _initializeClientId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(10000);
    _clientId = 'client_${timestamp}_$random';

    if (kDebugMode) {
      print('MQTT_LOGS:: Using Client ID: $_clientId');
    }
  }

  void _initializeMqttClient() {
    _client = MqttServerClient(mqttBrokerUrl, _clientId)
      ..useWebSocket = true
      ..port = _mqttPort
      ..logging(on: kDebugMode)
      ..keepAlivePeriod = _keepAlivePeriod
      ..onConnected = _onConnected
      ..onDisconnected = _onDisconnected
      ..onSubscribed = _onSubscribed
      ..onSubscribeFail = _onSubscribeFail
      ..pongCallback = _onPong
      ..setProtocolV311()
      ..autoReconnect = true
      ..connectTimeoutPeriod =
          10000; // Set connection timeout to 10000 ms (10 seconds)

    _client.connectionMessage = MqttConnectMessage()
        .withClientIdentifier(_clientId)
        .authenticateAs(MQTT_Websocket_USERNAME, MQTT_Websocket_PASSWORD)
        .keepAliveFor(_keepAlivePeriod)
        .startClean()
        .withWillQos(MqttQos.atLeastOnce);
  }

  void _onSubscribed(String topic) {
    if (kDebugMode) {
      print('MQTT_LOGS:: Subscribed to topic: $topic');
    }
  }

  void _onSubscribeFail(String topic) {
    if (kDebugMode) {
      print('MQTT_LOGS:: Failed to subscribe to topic: $topic');
    }
  }

  void _listenToConnectivityChanges() {
    // _connectivitySubscription =
    //     Connectivity().onConnectivityChanged.listen((result) {
    //   if (kDebugMode) {
    //     print('MQTT_LOGS:: Connectivity changed: $result');
    //   }

    //   if (result != ConnectivityResult.none) {
    //     if (!_isConnected && !_isConnecting) {
    //       _startReconnectTimer();
    //     }
    //   } else {
    //     disconnect();
    //   }
    // });
  }

  Future<bool> connect() async {
    if (_isDisposed) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Cannot connect, handler is disposed.');
      }
      return false;
    }

    if (_isConnected || _isConnecting) {
      if (kDebugMode) {
        print(
            'MQTT_LOGS:: Already connected or connecting. Skipping connect().');
      }
      return false;
    }

    _isConnecting = true;

    if (kDebugMode) {
      print('MQTT_LOGS:: Attempting to connect...');
    }

    try {
      await _client.connect();
    } on Exception catch (e) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Exception during connection: $e');
      }
      _isConnected = false;
      _isConnecting = false;
      _startReconnectTimer();
      return false;
    }

    if (_client.connectionStatus?.state == MqttConnectionState.connected) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Connected to MQTT broker');
      }
      _isConnected = true;
      _isConnecting = false;
      _subscribeToTopic();
      _reconnectAttempts = 0;
      onConnectedCallback?.call();
      return true;
    } else {
      if (kDebugMode) {
        print(
            'MQTT_LOGS:: Connection failed - status is ${_client.connectionStatus?.state}');
      }
      _isConnected = false;
      _isConnecting = false;
      _startReconnectTimer();
      return false;
    }
  }

  void _onDisconnected() {
    if (kDebugMode) {
      print('MQTT_LOGS:: Disconnected, Client ID: $_clientId');
    }
    _isConnected = false;
    _isConnecting = false;

    if (_client.connectionStatus?.disconnectionOrigin ==
        MqttDisconnectionOrigin.solicited) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Disconnection is solicited, not reconnecting');
      }
    } else {
      if (kDebugMode) {
        print(
            'MQTT_LOGS:: Unexpected disconnection, attempting to reconnect...');
      }
      _startReconnectTimer();
    }

    onDisconnectedCallback?.call();
  }

  void _startReconnectTimer() {
    if (_isDisposed) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Handler is disposed. Not starting reconnect timer.');
      }
      return;
    }

    // Limit the reconnect attempts to avoid flooding
    if (_reconnectAttempts >= _maxReconnectAttempts ||
        _isConnected ||
        _isConnecting) {
      print(
          'MQTT_LOGS:: Maximum reconnect attempts reached or already connected or connecting.');
      // Optional: Add a cooldown delay if maximum attempts are reached
      _reconnectTimer = Timer(Duration(minutes: 5), () {
        print('MQTT_LOGS:: Cooling down before reconnecting again.');
        _reconnectAttempts = 0; // Reset attempts after cooldown
      });
      return;
    }

    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print('MQTT_LOGS:: Reconnect timer is already running.');
      return;
    }

    _reconnectAttempts++;

    // Use exponential backoff with added jitter to reduce reconnect frequency
    int baseDelay = pow(2, _reconnectAttempts).toInt();
    int jitter =
        Random().nextInt(5); // Adds randomness to avoid reconnect spikes
    int waitTime = min(baseDelay + jitter, 60); // Cap at 60 seconds

    print('MQTT_LOGS:: Reconnecting in $waitTime seconds...');
    _reconnectTimer = Timer(Duration(seconds: waitTime), () async {
      if (_isDisposed) {
        if (kDebugMode) {
          print(
              'MQTT_LOGS:: Handler is disposed. Not attempting reconnection.');
        }
        return;
      }

      print(
          'MQTT_LOGS:: Attempting reconnection (Attempt $_reconnectAttempts)...');
      bool connected = await connect();
      if (!connected) {
        _startReconnectTimer();
      }
    });
  }

  void _subscribeToTopic() {
    final topic = '${userDevice?.trim() ?? 'default'}/msg';
    if (topic.isNotEmpty) {
      _client.subscribe(topic, MqttQos.atMostOnce);
      _updatesSubscription = _client.updates?.listen(_onMessageReceived);
      if (kDebugMode) {
        print('MQTT_LOGS:: Subscribed to topic: $topic');
      }
      // Send check command after subscribing
      checkDeviceOnline();
    } else {
      if (kDebugMode) {
        print('MQTT_LOGS:: Invalid topic, cannot subscribe');
      }
    }
  }

  void _onMessageReceived(List<MqttReceivedMessage<MqttMessage?>>? event) {
    if (_isDisposed) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Handler is disposed. Ignoring received message.');
      }
      return;
    }

    if (event == null || event.isEmpty) {
      if (kDebugMode) {
        print('MQTT_LOGS:: No messages received');
      }
      return;
    }

    for (var receivedMessage in event) {
      try {
        final MqttPublishMessage message =
            receivedMessage.payload as MqttPublishMessage;
        final String payload =
            MqttPublishPayload.bytesToStringAsString(message.payload.message);

        if (kDebugMode) {
          print(
              'MQTT_LOGS:: New data arrived: topic=<${receivedMessage.topic}>, payload=$payload');
        }

        String payloadText =
            payload.replaceFirst(' N', '').replaceFirst(' E', '');
        Map<String, dynamic> payloadMap = jsonDecode(payloadText);

        if (payloadMap.containsKey('id') && payloadMap.containsKey('status')) {
          _deviceOnlineCompleter?.complete(true);
          _deviceOnlineTimer?.cancel();
        }

        DeviceStatus deviceStatus = DeviceStatus.fromMap(payloadMap);
        deviceStatus.online = true;

        if (!_isDisposed) {
          data.value = deviceStatus;
        }

        HapticFeedback.vibrate();
      } catch (e, stackTrace) {
        if (kDebugMode) {
          print('MQTT_LOGS:: Error parsing message: $e');
          print(stackTrace);
        }
      }
    }
  }

  void _onConnected() {
    if (_isDisposed) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Connected callback called after disposal.');
      }
      return;
    }

    if (kDebugMode) {
      print('MQTT_LOGS:: Connected callback, Client ID: $_clientId');
    }
    _isConnected = true;
    _isConnecting = false;
    _reconnectAttempts = 0;
    _reconnectTimer?.cancel();
    onConnectedCallback?.call();
  }

  Future<bool> sendDeviceCommand(User user, String command) async {
    if (!_isConnected) {
      print('MQTT_LOGS:: Cannot send command. MQTT is not connected.');
      _startReconnectTimer();
      return false;
    }

    String deviceId = user.device?.deviceNumber ?? "unknown";
    if (deviceId == "unknown") {
      print('MQTT_LOGS:: Error: Device number is unknown');
      return false;
    }

    Map<String, String> commandMap = {"id": deviceId, "command": command};
    String jsonCommand = jsonEncode(commandMap);
    return _publishMessage(deviceId, jsonCommand);
  }

  void _onPong() {
    print('MQTT_LOGS:: Ping response client callback invoked');
  }

  bool _publishMessage(String topic, String message, {int retries = 3}) {
    if (_isDisposed) {
      if (kDebugMode) {
        print('MQTT_LOGS:: Cannot publish message, handler is disposed.');
      }
      return false;
    }

    if (_client.connectionStatus?.state == MqttConnectionState.connected) {
      final builder = MqttClientPayloadBuilder();
      builder.addString(message);
      _client.publishMessage(topic, MqttQos.atMostOnce, builder.payload!);
      print('MQTT_LOGS:: Published message to $topic: $message');
      return true;
    } else if (retries > 0) {
      print('MQTT_LOGS:: Client not connected, retrying in 2 seconds...');
      Future.delayed(Duration(seconds: 2), () {
        _publishMessage(topic, message, retries: retries - 1);
      });
      return false;
    } else {
      print('MQTT_LOGS:: Failed to publish after retries');
      return false;
    }
  }

  Future<void> disconnect() async {
    await _disconnect();
  }

  Future<void> _disconnect() async {
    print('MQTT_LOGS:: Disconnecting MQTT client');
    _isConnected = false;
    _isConnecting = false;
    _reconnectTimer?.cancel();
    _deviceOnlineTimer?.cancel();
    await _updatesSubscription?.cancel();
    _updatesSubscription = null;

    _client.disconnect();
    _client.pongCallback = null;
    _client.onConnected = null;
    _client.onDisconnected = null;
    _client.onSubscribed = null;
    _client.onSubscribeFail = null;
  }

  Future<void> dispose() async {
    if (_isDisposed) {
      return;
    }
    _isDisposed = true;
    await _disconnect();
    data.dispose();
  }

  Future<bool> checkDeviceOnline() async {
    if (_isDisposed) {
      print(
          'MQTT_LOGS:: Cannot check device online status, handler is disposed');
      return false;
    }

    if (!_isConnected || userDevice == null || userDevice!.isEmpty) {
      print('MQTT_LOGS:: Cannot check device online status');
      return false;
    }

    _deviceOnlineCompleter = Completer<bool>();

    String command = "check";
    Map<String, String> commandMap = {"id": userDevice!, "command": command};
    String jsonCommand = jsonEncode(commandMap);
    String mqttTopic = userDevice!.trim();

    print('MQTT_LOGS:: Publishing check command to topic: $mqttTopic');
    bool result = _publishMessage(mqttTopic, jsonCommand);

    if (!result) {
      print('MQTT_LOGS:: Failed to publish check command.');
      _deviceOnlineCompleter = null;
      return false;
    }

    _deviceOnlineTimer = Timer(Duration(seconds: _checkDeviceTimeout), () {
      _deviceOnlineCompleter?.complete(false);
    });

    return _deviceOnlineCompleter!.future;
  }
}
